2025-09-15 21:47:29.090 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 19:47:29.090457 to 2025-09-15 19:47:29.090457
2025-09-15 21:47:29.484 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-15 21:47:44.465 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-15 21:48:26.357 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-15 21:48:26.363 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-15 21:48:26.364 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-15 21:51:26.275 | INFO     | __main__:<module>:12 - Fetching data for BTC/USDT from 2023-09-26 19:51:26.275607 to 2025-09-15 19:51:26.275607
2025-09-15 21:51:26.845 | INFO     | app.services.backtesting.data:fetch_historical_data:73 - Fetching historical data for BTCUSDT from 2023-09-26 to 2025-09-15 with interval 1h
2025-09-15 21:51:39.386 | INFO     | app.services.backtesting.data:fetch_historical_data:97 - Successfully fetched 17281 data points.
2025-09-15 21:51:57.134 | INFO     | app.services.models.xgboost_strategy:generate_features:86 - Generating features...
2025-09-15 21:51:57.138 | INFO     | app.services.models.xgboost_strategy:generate_features:87 -             timestamp      open      high       low     close     volume  \
0 2023-09-26 00:00:00  26304.80  26307.73  26250.00  26266.00  540.39368   
1 2023-09-26 01:00:00  26266.01  26299.74  26234.37  26297.60  637.68208   
2 2023-09-26 02:00:00  26297.60  26360.00  26285.00  26357.14  564.22017   
3 2023-09-26 03:00:00  26357.14  26397.46  26345.28  26353.87  879.93385   
4 2023-09-26 04:00:00  26353.87  26363.84  26330.41  26344.67  355.98990   

      close_time quote_asset_volume  number_of_trades  \
0  1695689999999  14202562.71717360             24392   
1  1695693599999  16749168.64694030             24553   
2  1695697199999  14846803.19129890             22860   
3  1695700799999  23203242.98284640             25623   
4  1695704399999   9378770.48617770             15958   

  taker_buy_base_asset_volume taker_buy_quote_asset_volume ignore  
0                247.60394000             6507562.51115510      0  
1                365.25875000             9594972.70252490      0  
2                327.81683000             8626460.91875230      0  
3                487.72930000            12860466.24572410      0  
4                146.08616000             3848671.42776160      0  
2025-09-15 21:51:57.139 | INFO     | app.services.models.xgboost_strategy:generate_features:88 - (17281, 12)
2025-09-15 21:51:57.150 | WARNING  | app.services.models.xgboost_strategy:generate_features:99 - Column not found when dropping: "['symbol'] not found in axis"
2025-09-15 21:51:57.269 | INFO     | app.services.models.xgboost_strategy:generate_features:126 - Features generated successfully
