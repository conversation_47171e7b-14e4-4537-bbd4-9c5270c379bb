import pandas as pd
import numpy as np
import os
import sys
import joblib
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go
from loguru import logger

# Add project root to sys.path
# This is a common pattern to make project modules importable in notebooks
module_path = os.path.abspath(os.path.join('..'))
if module_path not in sys.path:
    sys.path.append(module_path)

# Import necessary components from our application
from app.services.backtesting.data_fetcher import BinanceDataFetcher
from app.services.models.xgboost_strategy import xgboost_strategy

# Scikit-learn and XGBoost
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
from xgboost import XGBClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix, classification_report

# Configure logger
logger.add("notebook_runtime.log", rotation="500 MB")

print("Setup complete. Modules imported and path configured.")

# Initialize the data fetcher
fetcher = BinanceDataFetcher()

# Define parameters
symbol = 'BTC/USDT'
timeframe = '1h'
days = 720
end_date = datetime.utcnow()
start_date = end_date - timedelta(days=days)

# Fetch the data
logger.info(f"Fetching data for {symbol} from {start_date} to {end_date}")
history_df = fetcher.fetch(symbol, timeframe, start_date, end_date)

# Display the first few rows and shape of the dataframe
print("Data fetched successfully.")
print("Shape of the dataframe:", history_df.shape)
history_df.head()

# Initialize the strategy to access the feature generation method
# Note: This will load the v1 model, but we are only using it for its `generate_features` method.
strategy = xgboost_strategy()

# Generate features
featured_df = strategy.generate_features(history_df.copy())

# Display the dataframe with new features
print("Features generated.")
print("Shape of the featured dataframe:", featured_df.shape)
featured_df.head()

# Define the target variable 'y'
# We predict if the next period's close is higher than the current period's close
featured_df['target'] = (featured_df['close'].shift(-1) > featured_df['close']).astype(int)

# Drop the last row since it will have a NaN target
featured_df.dropna(subset=['target'], inplace=True)

# Define features (X) and target (y)
features = strategy.features
X = featured_df[features]
y = featured_df['target']

print("Target variable 'y' created.")
print("Shape of X:", X.shape)
print("Shape of y:", y.shape)
print("\nValue counts of target variable:")
print(y.value_counts())

# Use TimeSeriesSplit for cross-validation
tscv = TimeSeriesSplit(n_splits=5)

# Get the last split for final train/test
for train_index, test_index in tscv.split(X):
    X_train, X_test = X.iloc[train_index], X.iloc[test_index]
    y_train, y_test = y.iloc[train_index], y.iloc[test_index]

print("Data split into training and testing sets.")
print("X_train shape:", X_train.shape)
print("X_test shape:", X_test.shape)
print("y_train shape:", y_train.shape)
print("y_test shape:", y_test.shape)

# 1. Initialize the StandardScaler
scaler = StandardScaler()

# 2. Fit the scaler on the training data and transform it
X_train_scaled = scaler.fit_transform(X_train)

# 3. Transform the test data using the same scaler
X_test_scaled = scaler.transform(X_test)

print("Features scaled correctly.")

# Initialize and train the XGBClassifier
model = XGBClassifier(
    n_estimators=100,
    max_depth=3,
    learning_rate=0.1,
    use_label_encoder=False,
    eval_metric='logloss'
)

print("Training XGBoost model...")
model.fit(X_train_scaled, y_train)
print("Model training complete.")

# Make predictions on the scaled test set
y_pred = model.predict(X_test_scaled)

# Print classification report
print("Classification Report:")
print(classification_report(y_test, y_pred))

# Print confusion matrix
print("\nConfusion Matrix:")
cm = confusion_matrix(y_test, y_pred)
print(cm)

# Visualize confusion matrix
fig = go.Figure(data=go.Heatmap(
                   z=cm,
                   x=['Predicted 0', 'Predicted 1'],
                   y=['Actual 0', 'Actual 1'],
                   hoverongaps=False))
fig.update_layout(title='Confusion Matrix')
fig.show()

# Define output paths
model_dir = os.path.join(module_path, 'app', 'services', 'models')
model_path = os.path.join(model_dir, 'XGBClassifier_v2.pkl')
scaler_path = os.path.join(model_dir, 'StandardScaler_v2.pkl')

# Save the model
joblib.dump(model, model_path)
print(f"Model saved to: {model_path}")

# Save the scaler
joblib.dump(scaler, scaler_path)
print(f"Scaler saved to: {scaler_path}")