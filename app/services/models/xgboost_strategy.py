import pandas as pd
from loguru import logger
import ta
import joblib
import os
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler
from xgboost import XGBClassifier
from sklearn.metrics import accuracy_score, precision_score, recall_score, f1_score, confusion_matrix


class xgboost_strategy:

    def __init__(self, n_estimators=100, max_depth=None, learning_rate=0.1):
        ### load model
        current_dir = os.path.dirname(os.path.abspath(__file__))
        model_path = os.path.join(current_dir, 'XGBClassifier_v1.pkl')
        self.model = joblib.load(model_path)
        self.features = ['open', 'high', 'low', 'close', 'macd', 'macd_signal', 'rsi', 'bb_high', 'bb_low', 'atr']

    # def generate_features(self, history):
    #     # Generate features based on historical data
    #     #Clean
    #     logger.info("Generating features for ...")
    #     logger.info(f"{history.head(5)}")
    #     logger.info(f"{history.shape}")
    #     history['date'] = pd.to_datetime(history['timestamp'])
    #     history.drop(columns=['symbol'], inplace=True)
    #     history.drop(columns=['timestamp'], inplace=True)

    #     # Create new features
    #     history['price_change'] = history['close'] - history['open']
    #     history['price_volatility'] = history['high'] - history['low']
    #     history['hourly_return'] = history['close'].pct_change()
    #     history['momentum'] = history['close'].diff()

    #     # 4. Moving averages 3 - 24hrs
    #     history['MA_3'] = history['close'].rolling(window=3).mean()
    #     history['MA_24'] = history['close'].rolling(window=24).mean()

    #     # 7. Time-based features
    #     history['hour'] = history['date'].dt.hour
    #     history['day_of_week'] = history['date'].dt.dayofweek

    #     # 8. Price levels (simple example - you might want to use a more sophisticated method)
    #     history['support'] = history['low'].rolling(window=24).min()
    #     history['resistance'] = history['high'].rolling(window=24).max()

    #     # 9. RSI
    #     def calculate_rsi(series, period=14):
    #         delta = series.diff()
    #         gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
    #         loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
    #         rs = gain / loss
    #         return 100 - (100 / (1 + rs))
        
    #     history['RSI'] = calculate_rsi(history['close'])

    #     # 10. Bollinger Bands
    #     history['BB_middle'] = history['close'].rolling(window=20).mean()
    #     history['BB_std'] = history['close'].rolling(window=20).std()
    #     history['BB_upper'] = history['BB_middle'] + (history['BB_std'] * 2)
    #     history['BB_lower'] = history['BB_middle'] - (history['BB_std'] * 2)

    #     # Drop rows with NaN values resulting from calculations
    #     history.dropna(inplace=True)

    #     # Function to add technical indicators
    #     def add_technical_indicators(df):
    #         df['macd'] = ta.trend.macd(df['close'])
    #         df['macd_signal'] = ta.trend.macd_signal(df['close'])
    #         df['rsi'] = ta.momentum.rsi(df['close'])
    #         df['bb_high'] = ta.volatility.bollinger_hband(df['close'])
    #         df['bb_low'] = ta.volatility.bollinger_lband(df['close'])
    #         df['atr'] = ta.volatility.average_true_range(df['high'], df['low'], df['close'])
    #         # Add technical indicators to the DataFrame
    #         # Avoid division by zero errors (replace 0s in 'atr')
    #         df['atr'].replace(0, 1e-6, inplace=True)
    #         return df

    #     history = add_technical_indicators(history)

    #     return history
    
    def generate_features(self, history):
        logger.info("Generating features...")
        logger.info(f"{history.head(5)}")
        logger.info(f"{history.shape}")
        
        # Make a copy to avoid modifying the original
        history = history.copy()
        
        # Convert timestamp
        history['date'] = pd.to_datetime(history['timestamp'])
        history = history.drop(columns=['symbol', 'timestamp'])
        
        # Add technical indicators first
        history['macd'] = ta.trend.macd(history['close'])
        history['macd_signal'] = ta.trend.macd_signal(history['close'])
        history['rsi'] = ta.momentum.rsi(history['close'])
        history['bb_high'] = ta.volatility.bollinger_hband(history['close'])
        history['bb_low'] = ta.volatility.bollinger_lband(history['close'])
        history['atr'] = ta.volatility.average_true_range(history['high'], history['low'], history['close'])
        
        # Forward fill NaN values
        history = history.ffill()
        
        # If still any NaN values at the start, backward fill
        history = history.bfill()
        
        # Ensure all required features are present and non-null
        required_features = self.features
        missing_features = [f for f in required_features if f not in history.columns]
        if missing_features:
            raise ValueError(f"Missing required features: {missing_features}")
        
        # Verify no NaN values remain
        if history[required_features].isna().any().any():
            raise ValueError("NaN values present in features after filling")
        
        logger.info("Features generated successfully")

        return history
    


